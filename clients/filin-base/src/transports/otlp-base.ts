/**
 * Базовая реализация OTLP транспорта
 * Содержит общую логику для всех платформ
 */

import { TraceEvent, OTLPConfig } from './types';
import { CanonicalFields } from '../trace/CanonicalFields';

export interface OTLPSpanBase {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  name: string;
  kind: number;
  startTimeUnixNano: string;
  endTimeUnixNano?: string;
  attributes: Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }>;
  events?: Array<{
    timeUnixNano: string;
    name: string;
    attributes: Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }>;
  }>;
  status?: { code: number; message?: string };
  links?: Array<{
    traceId: string;
    spanId: string;
    attributes: Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }>;
  }>;
}

export interface OTLPResourceBase {
  attributes: Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }>;
}

export interface OTLPPayloadBase {
  resourceSpans: Array<{
    resource: OTLPResourceBase;
    scopeSpans: Array<{
      scope: {
        name: string;
        version: string;
      };
      spans: OTLPSpanBase[];
    }>;
  }>;
}

/**
 * Базовый класс для OTLP транспортов
 */
export abstract class OTLPTransportBase {
  protected config: OTLPConfig;
  protected spanBuffer = new Map<string, { start?: TraceEvent; events: TraceEvent[] }>();

  constructor(config: OTLPConfig) {
    this.config = {
      compression: 'none',
      batchSize: 50,
      batchTimeout: 10000,
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    };
  }

  /**
   * Добавляет событие в буфер спанов для агрегации
   */
  public addEvent(event: TraceEvent): void {
    const spanKey = event[CanonicalFields.SPAN_ID];

    if (!this.spanBuffer.has(spanKey)) {
      this.spanBuffer.set(spanKey, { events: [] });
    }

    const spanData = this.spanBuffer.get(spanKey)!;

    if (event.event === 'SPAN_START') {
      spanData.start = event;

      // Отладочный вывод
      if (process.env.FILIN_DEBUG_OTLP) {
        console.log(`🔍 OTLPTransportBase addEvent(SPAN_START): spanId=${spanKey}`);
      }
    } else if (event.event === 'SPAN_END') {
      spanData.events.push(event);

      // Отладочный вывод
      if (process.env.FILIN_DEBUG_OTLP) {
        console.log(`🔍 OTLPTransportBase addEvent(SPAN_END): spanId=${spanKey}`);
        console.log(`🔍 Span now has start=${!!spanData.start}, events=${spanData.events.length}`);
      }
    } else {
      spanData.events.push(event);
    }
  }

  /**
   * Группирует события по спанам (deprecated - используйте addEvent)
   */
  protected groupEventsBySpans(events: TraceEvent[]): void {
    for (const event of events) {
      this.addEvent(event);
    }
  }

  /**
   * Конвертирует события в OTLP формат
   */
  protected convertToOTLP(serviceName: string, serviceVersion: string, environment: string): OTLPPayloadBase {
    const spans: OTLPSpanBase[] = [];

    // Отладочный вывод для диагностики
    if (process.env.FILIN_DEBUG_OTLP) {
      console.log('🔍 OTLPTransportBase convertToOTLP Debug:');
      console.log('  Service:', serviceName);
      console.log('  Span buffer size:', this.spanBuffer.size);
      console.log('  Span keys:', Array.from(this.spanBuffer.keys()));
      for (const [spanKey, spanData] of this.spanBuffer) {
        console.log(`  Span ${spanKey}:`, {
          hasStart: !!spanData.start,
          eventsCount: spanData.events.length,
          events: spanData.events.map(e => e.event),
          startEvent: spanData.start?.event,
          endEvent: spanData.events.find(e => e.event === 'SPAN_END')?.event
        });
      }
    }

    for (const [spanKey, spanData] of this.spanBuffer) {
      if (!spanData.start) continue; // Пропускаем спаны без SPAN_START

      const startEvent = spanData.start;
      const endEvent = spanData.events.find(e => e.event === 'SPAN_END');

      const span: OTLPSpanBase = {
        traceId: this.normalizeHexId(startEvent[CanonicalFields.TRACE_ID], 32),
        spanId: this.normalizeHexId(startEvent[CanonicalFields.SPAN_ID], 16),
        parentSpanId: startEvent[CanonicalFields.PARENT_SPAN_ID] ?
          this.normalizeHexId(startEvent[CanonicalFields.PARENT_SPAN_ID] as string, 16) : undefined,
        name: startEvent.operation || startEvent.component || startEvent.event,
        kind: 1, // SPAN_KIND_INTERNAL
        startTimeUnixNano: startEvent[CanonicalFields.TIMESTAMP_UNIX_NANOS],
        endTimeUnixNano: endEvent?.[CanonicalFields.TIMESTAMP_UNIX_NANOS],
        attributes: this.convertAttributes(endEvent || startEvent),
        events: spanData.events
          .filter(e => e.event !== 'SPAN_END')
          .map(e => ({
            timeUnixNano: e[CanonicalFields.TIMESTAMP_UNIX_NANOS],
            name: e.event,
            attributes: this.convertAttributes(e)
          }))
      };

      if (endEvent?.[CanonicalFields.STOP_REASON] === 'error' || endEvent?.[CanonicalFields.ERROR_MESSAGE]) {
        span.status = {
          code: 2, // STATUS_CODE_ERROR
          message: endEvent[CanonicalFields.ERROR_MESSAGE] as string
        };
      }

      // Отладочный вывод для диагностики
      if (process.env.FILIN_DEBUG_OTLP) {
        const eventForAttributes = endEvent || startEvent;
        console.log('🔍 OTLP Span Debug:');
        console.log('  Using event:', endEvent ? 'SPAN_END' : 'SPAN_START');
        console.log('  Operation:', eventForAttributes[CanonicalFields.OPERATION]);
        console.log('  HTTP Method:', eventForAttributes[CanonicalFields.HTTP_REQUEST_METHOD]);
        console.log('  HTTP Status:', eventForAttributes[CanonicalFields.HTTP_RESPONSE_STATUS_CODE]);
        console.log('  Idempotency Key:', eventForAttributes[CanonicalFields.REQUEST_IDEMPOTENCY_KEY]);
        console.log('  Attributes count:', span.attributes.length);
        console.log('  Converted attributes:', span.attributes.map(a => `${a.key}=${JSON.stringify(a.value)}`));
      }

      spans.push(span);
    }

    // НЕ очищаем буфер автоматически - spans должны накапливаться
    // до получения полной пары SPAN_START/SPAN_END
    // Очистка будет происходить только при явном вызове flush() или по таймауту

    return {
      resourceSpans: [{
        resource: {
          attributes: [
            { key: 'service.name', value: { stringValue: serviceName } },
            { key: 'service.version', value: { stringValue: serviceVersion } },
            { key: 'service.namespace', value: { stringValue: 'filin' } },
            { key: 'deployment.environment', value: { stringValue: environment } }
          ]
        },
        scopeSpans: [{
          scope: {
            name: 'filin-trace',
            version: '1.0.0'
          },
          spans
        }]
      }]
    };
  }

  /**
   * Конвертирует атрибуты события
   */
  protected convertAttributes(event: TraceEvent): Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }> {
    const attributes: Array<{ key: string; value: { stringValue?: string; intValue?: string; boolValue?: boolean } }> = [];

    // Конвертируем основные поля
    const fieldsToConvert = [
      // Основные поля
      CanonicalFields.COMPONENT,
      CanonicalFields.OPERATION,
      CanonicalFields.DIRECTION,
      CanonicalFields.STOP_REASON,
      CanonicalFields.DATA_CLASSIFICATION,

      // HTTP поля
      CanonicalFields.HTTP_REQUEST_METHOD,
      CanonicalFields.URL_PATH,
      CanonicalFields.URL_QUERY,
      CanonicalFields.URL_FULL,
      CanonicalFields.HTTP_ROUTE,
      CanonicalFields.USER_AGENT_ORIGINAL,

      // Network поля
      CanonicalFields.SERVER_ADDRESS,
      CanonicalFields.CLIENT_ADDRESS,
      CanonicalFields.NETWORK_PEER_ADDRESS,

      // RPC/LSP поля
      CanonicalFields.RPC_SYSTEM,
      CanonicalFields.RPC_METHOD,
      CanonicalFields.RPC_JSONRPC_REQUEST_ID,

      // GenAI поля
      CanonicalFields.GEN_AI_PROVIDER_NAME,
      CanonicalFields.GEN_AI_REQUEST_MODEL,

      // Runtime поля
      CanonicalFields.RUNTIME_NAME,
      CanonicalFields.RUNTIME_VERSION,
      CanonicalFields.RUNTIME_DESCRIPTION,

      // Document поля (LSP/IDE)
      CanonicalFields.DOCUMENT_LANGUAGE_ID,
      CanonicalFields.DOCUMENT_FILE_NAME,

      // Chat поля
      CanonicalFields.CHAT_MESSAGE_HASH,
      CanonicalFields.CHAT_RESPONSE_HASH,

      // Investigation поля
      CanonicalFields.SESSION_ID,
      CanonicalFields.IDE_NAME,
      CanonicalFields.IDE_VERSION,
      CanonicalFields.PLUGIN_VERSION,
      CanonicalFields.OS,
      CanonicalFields.WORKSPACE_ID,
      CanonicalFields.REPO_HASH,
      CanonicalFields.USER_ANONYMOUS_ID,
      CanonicalFields.REQUEST_IDEMPOTENCY_KEY,

      // Error поля
      CanonicalFields.ERROR_TYPE,
      CanonicalFields.ERROR_MESSAGE,

      // Streaming поля
      CanonicalFields.STREAM_ID,
      CanonicalFields.SLOW_KIND,
      CanonicalFields.HANG_KIND
    ];

    for (const field of fieldsToConvert) {
      const value = (event as any)[field];
      if (value !== undefined) {
        if (typeof value === 'string') {
          attributes.push({ key: field, value: { stringValue: value } });
        } else if (typeof value === 'number') {
          attributes.push({ key: field, value: { intValue: value.toString() } });
        } else if (typeof value === 'boolean') {
          attributes.push({ key: field, value: { boolValue: value } });
        }
      }
    }

    // Добавляем числовые метрики
    const numericFields = [
      CanonicalFields.DURATION_MS,
      CanonicalFields.HTTP_RESPONSE_STATUS_CODE,
      CanonicalFields.SERVER_PORT,
      CanonicalFields.NETWORK_PEER_PORT,
      CanonicalFields.RPC_JSONRPC_PARAMS_BYTES,
      CanonicalFields.RPC_JSONRPC_RESULT_BYTES,
      CanonicalFields.GEN_AI_USAGE_INPUT_TOKENS,
      CanonicalFields.GEN_AI_USAGE_OUTPUT_TOKENS,
      CanonicalFields.DOCUMENT_POSITION_LINE,
      CanonicalFields.DOCUMENT_POSITION_CHARACTER,
      CanonicalFields.DOCUMENT_LENGTH,
      CanonicalFields.COMPLETION_COUNT,
      CanonicalFields.CHAT_MESSAGE_LENGTH,
      CanonicalFields.CHAT_RESPONSE_LENGTH,
      CanonicalFields.IDE_SESSION_SEQ,
      CanonicalFields.STREAM_SEQ,
      CanonicalFields.STREAM_CHUNKS_TOTAL,
      CanonicalFields.BYTES_TOTAL,
      CanonicalFields.TIME_TO_FIRST_TOKEN,
      CanonicalFields.TIME_TO_LAST_BYTE,
      CanonicalFields.CHUNK_INDEX
    ];
    for (const field of numericFields) {
      const value = (event as any)[field];
      if (typeof value === 'number') {
        attributes.push({ key: field, value: { intValue: value.toString() } });
      }
    }

    // Добавляем булевы поля
    const booleanFields = [
      CanonicalFields.COMPLETION_HAS_RESULTS,
      CanonicalFields.ERROR_STACK_TRUNCATED
    ];
    for (const field of booleanFields) {
      const value = (event as any)[field];
      if (typeof value === 'boolean') {
        attributes.push({ key: field, value: { boolValue: value } });
      }
    }

    return attributes;
  }

  /**
   * Нормализует hex ID для OTLP (hex формат, не base64)
   */
  protected normalizeHexId(hex: string, expectedLength: number): string {
    // Убираем все не-hex символы
    let normalizedHex = hex.replace(/[^0-9a-fA-F]/g, '');

    // Приводим к нужной длине
    if (normalizedHex.length < expectedLength) {
      normalizedHex = normalizedHex.padStart(expectedLength, '0');
    } else if (normalizedHex.length > expectedLength) {
      normalizedHex = normalizedHex.substring(0, expectedLength);
    }

    return normalizedHex.toLowerCase();
  }

  /**
   * Конвертирует hex строку в base64 для OTLP (deprecated - используйте normalizeHexId)
   * @deprecated OTLP использует hex формат, не base64
   */
  protected hexToBase64(hex: string): string {
    // Нормализуем hex строку
    let normalizedHex = hex.replace(/[^0-9a-fA-F]/g, '');

    // Определяем длину: trace ID = 32 символа, span ID = 16 символов
    if (normalizedHex.length <= 16) {
      normalizedHex = normalizedHex.padStart(16, '0');
    } else {
      normalizedHex = normalizedHex.padStart(32, '0').substring(0, 32);
    }

    // Конвертируем в байты
    const bytes = new Uint8Array(normalizedHex.length / 2);
    for (let i = 0; i < normalizedHex.length; i += 2) {
      bytes[i / 2] = parseInt(normalizedHex.substring(i, i + 2), 16);
    }

    // Конвертируем в base64
    if (typeof btoa !== 'undefined') {
      // Браузер
      return btoa(String.fromCharCode(...bytes));
    } else {
      // Node.js
      return Buffer.from(bytes).toString('base64');
    }
  }

  /**
   * Retry логика с exponential backoff
   */
  protected async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.config.maxRetries || 3,
    baseDelay: number = this.config.retryDelay || 1000
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt);
          await this.sleep(delay);
        }
      }
    }

    throw lastError;
  }

  /**
   * Sleep utility
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }



  /**
   * Парсит заголовки из строки формата "key1=value1,key2=value2"
   */
  public static parseHeaders(headersStr: string): Record<string, string> {
    const headers: Record<string, string> = {};

    try {
      const pairs = headersStr.split(',');
      for (const pair of pairs) {
        const [key, value] = pair.split('=', 2);
        if (key && value) {
          headers[key.trim()] = value.trim();
        }
      }
    } catch (error) {
      console.warn('Failed to parse OTLP headers:', headersStr, error);
    }

    return headers;
  }

  /**
   * Создает конфигурацию для Jaeger
   */
  static createJaegerConfig(endpoint: string): Partial<OTLPConfig> {
    return {
      enabled: true,
      endpoint,
      protocol: 'http',
      headers: {
        'Content-Type': 'application/json'
      },
      batchSize: 50,
      batchTimeout: 10000,
      maxRetries: 3,
      retryDelay: 1000
    };
  }

  /**
   * Создает конфигурацию для Grafana
   */
  static createGrafanaConfig(endpoint: string, apiKey?: string): Partial<OTLPConfig> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }

    return {
      enabled: true,
      endpoint,
      protocol: 'http',
      headers,
      batchSize: 100,
      batchTimeout: 5000,
      maxRetries: 5,
      retryDelay: 2000
    };
  }
}